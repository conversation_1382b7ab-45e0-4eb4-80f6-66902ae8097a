import React from 'react';
import { X } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { modal } from '@/lib/responsive';

interface ResponsiveModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | 'full';
  className?: string;
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
}

const ResponsiveModal: React.FC<ResponsiveModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  footer,
  size = 'lg',
  className,
  showCloseButton = true,
  closeOnOverlayClick = true,
}) => {
  const { isRTL } = useLanguage();
  const isMobile = useIsMobile();

  const getSizeClasses = () => {
    const sizeConfig = modal.sizes[size as keyof typeof modal.sizes];
    if (sizeConfig) {
      return isMobile ? sizeConfig.mobile : sizeConfig.sm;
    }

    // Fallback for custom sizes
    const fallbackSizes = {
      sm: isMobile ? 'max-w-sm mx-2' : 'max-w-sm',
      md: isMobile ? 'max-w-md mx-2' : 'max-w-md',
      lg: isMobile ? 'max-w-lg mx-2' : 'max-w-2xl',
      xl: isMobile ? 'max-w-xl mx-2' : 'max-w-4xl',
      '2xl': isMobile ? 'max-w-2xl mx-2' : 'max-w-6xl',
      '3xl': isMobile ? 'max-w-3xl mx-2' : 'max-w-7xl',
      '4xl': isMobile ? 'max-w-4xl mx-2' : 'max-w-[90rem]',
      full: 'max-w-[95vw]',
    };

    return fallbackSizes[size as keyof typeof fallbackSizes] || fallbackSizes.lg;
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleKeyDown = React.useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  }, [onClose]);

  React.useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, handleKeyDown]);

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      onClick={handleOverlayClick}
    >
      <div className={cn(
        'bg-background border border-border rounded-xl shadow-xl w-full overflow-hidden',
        'transform transition-all duration-200 ease-out',
        'animate-in fade-in-0 zoom-in-95 slide-in-from-bottom-2',
        isMobile ? modal.heights.mobile : modal.heights.sm,
        getSizeClasses(),
        className
      )}>
        {/* Header */}
        <div className={cn(
          'flex items-center justify-between border-b border-border bg-card/50',
          isMobile ? modal.padding.mobile : modal.padding.sm,
          isRTL && 'flex-row-reverse'
        )}>
          <div className="flex-1 min-w-0">
            <h2 className="text-lg sm:text-xl font-semibold text-foreground truncate">
              {title}
            </h2>
            {description && (
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                {description}
              </p>
            )}
          </div>
          {showCloseButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className={cn(
                'h-8 w-8 p-0 hover:bg-muted flex-shrink-0',
                isRTL ? 'ml-4' : 'mr-0'
              )}
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Content */}
        <div className="overflow-y-auto flex-1">
          {children}
        </div>

        {/* Footer */}
        {footer && (
          <div className={cn(
            'border-t border-border bg-card/30',
            isMobile ? modal.padding.mobile : modal.padding.sm,
            isRTL && 'flex-row-reverse'
          )}>
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default ResponsiveModal;
