import React from 'react';
import { X } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { modal } from '@/lib/responsive';

interface ResponsiveModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | 'full';
  className?: string;
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
}

const ResponsiveModal: React.FC<ResponsiveModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  footer,
  size = 'lg',
  className,
  showCloseButton = true,
  closeOnOverlayClick = true,
}) => {
  const { isRTL } = useLanguage();
  const isMobile = useIsMobile();

  const getSizeClasses = () => {
    const sizeConfig = modal.sizes[size as keyof typeof modal.sizes];
    if (sizeConfig) {
      return isMobile ? sizeConfig.mobile : sizeConfig.sm;
    }

    // Fallback for custom sizes
    const fallbackSizes = {
      sm: isMobile ? 'max-w-sm mx-2' : 'max-w-sm',
      md: isMobile ? 'max-w-md mx-2' : 'max-w-md',
      lg: isMobile ? 'max-w-lg mx-2' : 'max-w-2xl',
      xl: isMobile ? 'max-w-xl mx-2' : 'max-w-4xl',
      '2xl': isMobile ? 'max-w-2xl mx-2' : 'max-w-6xl',
      '3xl': isMobile ? 'max-w-3xl mx-2' : 'max-w-7xl',
      '4xl': isMobile ? 'max-w-4xl mx-2' : 'max-w-[90rem]',
      full: isMobile ? 'max-w-[95vw] mx-2' : 'max-w-[95vw]',
    };

    return fallbackSizes[size as keyof typeof fallbackSizes] || fallbackSizes.lg;
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  const modalRef = React.useRef<HTMLDivElement>(null);

  const handleKeyDown = React.useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }

    // Trap focus within modal
    if (e.key === 'Tab' && modalRef.current) {
      const focusableElements = modalRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus();
          e.preventDefault();
        }
      }
    }
  }, [onClose]);

  React.useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';

      // Focus the modal when it opens
      setTimeout(() => {
        const firstFocusable = modalRef.current?.querySelector(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        ) as HTMLElement;
        firstFocusable?.focus();
      }, 100);
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, handleKeyDown]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4"
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      aria-describedby={description ? "modal-description" : undefined}
    >
      <div
        ref={modalRef}
        className={cn(
          'bg-background border border-border rounded-xl shadow-xl w-full',
          'transform transition-all duration-200 ease-out',
          'animate-in fade-in-0 zoom-in-95 slide-in-from-bottom-2',
          'flex flex-col',
          // Better height management
          isMobile
            ? 'max-h-[95vh] min-h-[50vh]'
            : 'max-h-[90vh] min-h-[40vh]',
          getSizeClasses(),
          className
        )}
      >
        {/* Header - Fixed */}
        <div className={cn(
          'flex items-center justify-between border-b border-border bg-card/50 flex-shrink-0',
          isMobile ? modal.padding.mobile : modal.padding.sm,
          isRTL && 'flex-row-reverse'
        )}>
          <div className="flex-1 min-w-0">
            <h2
              id="modal-title"
              className="text-lg sm:text-xl font-semibold text-foreground truncate"
            >
              {title}
            </h2>
            {description && (
              <p
                id="modal-description"
                className="text-sm text-muted-foreground mt-1 line-clamp-2"
              >
                {description}
              </p>
            )}
          </div>
          {showCloseButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className={cn(
                'h-8 w-8 p-0 hover:bg-muted flex-shrink-0',
                isRTL ? 'ml-4' : 'mr-0'
              )}
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Content - Scrollable */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
          <div className="min-h-0">
            {children}
          </div>
        </div>

        {/* Footer - Fixed */}
        {footer && (
          <div className={cn(
            'border-t border-border bg-card/30 flex-shrink-0',
            isMobile ? modal.padding.mobile : modal.padding.sm,
            isRTL && 'flex-row-reverse'
          )}>
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default ResponsiveModal;
