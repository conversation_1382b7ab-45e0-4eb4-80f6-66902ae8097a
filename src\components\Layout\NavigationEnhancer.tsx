import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface NavigationEnhancerProps {
  children: React.ReactNode;
  className?: string;
  enableProgressBar?: boolean;
  enableFadeTransition?: boolean;
}

/**
 * NavigationEnhancer - Enhances navigation experience
 * Provides loading states, progress indicators, and smooth transitions
 */
const NavigationEnhancer: React.FC<NavigationEnhancerProps> = ({
  children,
  className,
  enableProgressBar = true,
  enableFadeTransition = true,
}) => {
  const location = useLocation();
  const [isNavigating, setIsNavigating] = useState(false);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    // Start navigation animation
    setIsNavigating(true);
    setProgress(0);

    // Simulate loading progress
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + Math.random() * 30;
      });
    }, 100);

    // Complete navigation after a short delay
    const navigationTimer = setTimeout(() => {
      setProgress(100);
      setTimeout(() => {
        setIsNavigating(false);
        setProgress(0);
      }, 200);
    }, 300);

    return () => {
      clearInterval(progressInterval);
      clearTimeout(navigationTimer);
    };
  }, [location.pathname]);

  return (
    <div className={cn('relative', className)}>
      {/* Progress Bar */}
      {enableProgressBar && isNavigating && (
        <div className="fixed top-0 left-0 right-0 z-50 h-1 bg-background/20">
          <div 
            className="h-full bg-primary transition-all duration-300 ease-out"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}

      {/* Content with fade transition */}
      <div className={cn(
        enableFadeTransition && 'transition-opacity duration-200',
        isNavigating && enableFadeTransition ? 'opacity-90' : 'opacity-100'
      )}>
        {children}
      </div>

      {/* Loading overlay for very slow transitions */}
      {isNavigating && progress < 50 && (
        <div className="fixed inset-0 bg-background/5 backdrop-blur-[1px] z-40 pointer-events-none" />
      )}
    </div>
  );
};

export default NavigationEnhancer;
