import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { getResponsiveClasses } from '@/lib/responsive';

interface PageHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
  showBorder?: boolean;
  variant?: 'default' | 'centered' | 'compact';
  size?: 'sm' | 'md' | 'lg';
}

/**
 * PageHeader - Standardized page header component
 * Provides consistent title, description, and action layout
 */
const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  actions,
  className,
  showBorder = true,
  variant = 'default',
  size = 'md',
}) => {
  const { isRTL } = useLanguage();

  const sizeClasses = {
    sm: {
      title: 'text-xl sm:text-2xl',
      description: 'text-sm sm:text-base',
      spacing: 'space-y-2',
      padding: 'pb-4',
    },
    md: {
      title: getResponsiveClasses.typography('pageTitle'),
      description: getResponsiveClasses.typography('description'),
      spacing: 'space-y-3',
      padding: 'pb-6',
    },
    lg: {
      title: 'text-3xl sm:text-4xl lg:text-5xl',
      description: 'text-base sm:text-lg lg:text-xl',
      spacing: 'space-y-4',
      padding: 'pb-8',
    },
  };

  const variantClasses = {
    default: '',
    centered: 'text-center',
    compact: 'space-y-2',
  };

  return (
    <div className={cn(
      sizeClasses[size].spacing,
      showBorder && `${sizeClasses[size].padding} border-b border-border/50`,
      variantClasses[variant],
      isRTL && variant !== 'centered' && 'text-right',
      className
    )}>
      <div className={cn(
        'flex items-start justify-between gap-4',
        isRTL && 'flex-row-reverse',
        variant === 'centered' && 'flex-col items-center gap-3'
      )}>
        <div className={cn(
          'flex-1 min-w-0',
          variant === 'centered' && 'flex-none text-center'
        )}>
          <h1 className={cn(
            'font-bold text-foreground tracking-tight',
            sizeClasses[size].title
          )}>
            {title}
          </h1>
          {description && (
            <p className={cn(
              'text-muted-foreground mt-2 leading-relaxed',
              sizeClasses[size].description
            )}>
              {description}
            </p>
          )}
        </div>
        {actions && (
          <div className={cn(
            'flex-shrink-0',
            variant === 'centered' && 'flex-none'
          )}>
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

export default PageHeader;
