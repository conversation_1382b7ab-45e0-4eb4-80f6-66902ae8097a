import React from 'react';
import TasksList from '@/components/Tasks/TasksList';
import { useLanguage } from '@/contexts/LanguageContext';
import { MainLayout, ContentContainer, PageHeader, PageTransition } from '@/components/Layout';

const Tasks: React.FC = () => {
  const { t } = useLanguage();

  return (
    <MainLayout>
      <PageTransition type="slide" direction="up">
        <ContentContainer>
          <PageHeader
            title={t('tasks.title')}
            description={t('tasks.subtitle')}
          />
          <TasksList />
        </ContentContainer>
      </PageTransition>
    </MainLayout>
  );
};

export default Tasks;
