import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  taskService, 
  projectService, 
  noteService,
  type Task,
  type Project,
  type Note,
  type TaskFilter,
  type ProjectFilter,
  type NoteFilter,
  type PaginationOptions
} from '@/services';

// Query Keys
export const queryKeys = {
  tasks: {
    all: ['tasks'] as const,
    lists: () => [...queryKeys.tasks.all, 'list'] as const,
    list: (filter: TaskFilter, pagination: PaginationOptions) => 
      [...queryKeys.tasks.lists(), filter, pagination] as const,
    details: () => [...queryKeys.tasks.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.tasks.details(), id] as const,
    stats: () => [...queryKeys.tasks.all, 'stats'] as const,
    overdue: () => [...queryKeys.tasks.all, 'overdue'] as const,
    dueToday: () => [...queryKeys.tasks.all, 'dueToday'] as const,
    byProject: (projectId: string) => [...queryKeys.tasks.all, 'byProject', projectId] as const,
  },
  projects: {
    all: ['projects'] as const,
    lists: () => [...queryKeys.projects.all, 'list'] as const,
    list: (filter: ProjectFilter, pagination: PaginationOptions) => 
      [...queryKeys.projects.lists(), filter, pagination] as const,
    details: () => [...queryKeys.projects.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.projects.details(), id] as const,
    stats: () => [...queryKeys.projects.all, 'stats'] as const,
    active: () => [...queryKeys.projects.all, 'active'] as const,
    overdue: () => [...queryKeys.projects.all, 'overdue'] as const,
    withTasks: (id: string) => [...queryKeys.projects.details(), id, 'withTasks'] as const,
  },
  notes: {
    all: ['notes'] as const,
    lists: () => [...queryKeys.notes.all, 'list'] as const,
    list: (filter: NoteFilter, pagination: PaginationOptions) => 
      [...queryKeys.notes.lists(), filter, pagination] as const,
    details: () => [...queryKeys.notes.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.notes.details(), id] as const,
    stats: () => [...queryKeys.notes.all, 'stats'] as const,
    pinned: () => [...queryKeys.notes.all, 'pinned'] as const,
    recent: (limit: number) => [...queryKeys.notes.all, 'recent', limit] as const,
    byProject: (projectId: string) => [...queryKeys.notes.all, 'byProject', projectId] as const,
    byTask: (taskId: string) => [...queryKeys.notes.all, 'byTask', taskId] as const,
  },
};

// Task Hooks
export function useTasks(filter: TaskFilter = {}, pagination: PaginationOptions = { page: 1, limit: 50 }) {
  return useQuery({
    queryKey: queryKeys.tasks.list(filter, pagination),
    queryFn: () => taskService.getTasks(filter, pagination),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

export function useTask(id: string) {
  return useQuery({
    queryKey: queryKeys.tasks.detail(id),
    queryFn: () => taskService.getTaskById(id),
    enabled: !!id,
  });
}

export function useTaskStats() {
  return useQuery({
    queryKey: queryKeys.tasks.stats(),
    queryFn: () => taskService.getTaskStats(),
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
}

export function useOverdueTasks() {
  return useQuery({
    queryKey: queryKeys.tasks.overdue(),
    queryFn: () => taskService.getOverdueTasks(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

export function useTasksDueToday() {
  return useQuery({
    queryKey: queryKeys.tasks.dueToday(),
    queryFn: () => taskService.getTasksDueToday(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

export function useTasksByProject(projectId: string) {
  return useQuery({
    queryKey: queryKeys.tasks.byProject(projectId),
    queryFn: () => taskService.getTasksByProject(projectId),
    enabled: !!projectId,
  });
}

// Task Mutations
export function useCreateTask() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => 
      taskService.createTask(taskData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks.all });
    },
  });
}

export function useUpdateTask() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Omit<Task, 'id' | 'createdAt'>> }) => 
      taskService.updateTask(id, updates),
    onSuccess: (updatedTask) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks.all });
      queryClient.setQueryData(queryKeys.tasks.detail(updatedTask.id), updatedTask);
    },
  });
}

export function useDeleteTask() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => taskService.deleteTask(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks.all });
    },
  });
}

export function useCompleteTask() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => taskService.completeTask(id),
    onSuccess: (updatedTask) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks.all });
      queryClient.setQueryData(queryKeys.tasks.detail(updatedTask.id), updatedTask);
    },
  });
}

// Project Hooks
export function useProjects(filter: ProjectFilter = {}, pagination: PaginationOptions = { page: 1, limit: 50 }) {
  return useQuery({
    queryKey: queryKeys.projects.list(filter, pagination),
    queryFn: () => projectService.getProjects(filter, pagination),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

export function useProject(id: string) {
  return useQuery({
    queryKey: queryKeys.projects.detail(id),
    queryFn: () => projectService.getProjectById(id),
    enabled: !!id,
  });
}

export function useProjectStats() {
  return useQuery({
    queryKey: queryKeys.projects.stats(),
    queryFn: () => projectService.getProjectStats(),
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
}

export function useActiveProjects() {
  return useQuery({
    queryKey: queryKeys.projects.active(),
    queryFn: () => projectService.getActiveProjects(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

export function useProjectWithTasks(id: string) {
  return useQuery({
    queryKey: queryKeys.projects.withTasks(id),
    queryFn: () => projectService.getProjectWithTasks(id),
    enabled: !!id,
  });
}

// Project Mutations
export function useCreateProject() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => 
      projectService.createProject(projectData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.projects.all });
    },
  });
}

export function useUpdateProject() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Omit<Project, 'id' | 'createdAt'>> }) => 
      projectService.updateProject(id, updates),
    onSuccess: (updatedProject) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.projects.all });
      queryClient.setQueryData(queryKeys.projects.detail(updatedProject.id), updatedProject);
    },
  });
}

export function useDeleteProject() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => projectService.deleteProject(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.projects.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks.all });
    },
  });
}

// Note Hooks
export function useNotes(filter: NoteFilter = {}, pagination: PaginationOptions = { page: 1, limit: 50 }) {
  return useQuery({
    queryKey: queryKeys.notes.list(filter, pagination),
    queryFn: () => noteService.getNotes(filter, pagination),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

export function useNote(id: string) {
  return useQuery({
    queryKey: queryKeys.notes.detail(id),
    queryFn: () => noteService.getNoteById(id),
    enabled: !!id,
  });
}

export function useNoteStats() {
  return useQuery({
    queryKey: queryKeys.notes.stats(),
    queryFn: () => noteService.getNoteStats(),
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
}

export function usePinnedNotes() {
  return useQuery({
    queryKey: queryKeys.notes.pinned(),
    queryFn: () => noteService.getPinnedNotes(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

export function useRecentNotes(limit: number = 10) {
  return useQuery({
    queryKey: queryKeys.notes.recent(limit),
    queryFn: () => noteService.getRecentNotes(limit),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Note Mutations
export function useCreateNote() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (noteData: Omit<Note, 'id' | 'createdAt' | 'updatedAt' | 'wordCount'>) => 
      noteService.createNote(noteData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.notes.all });
    },
  });
}

export function useUpdateNote() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Omit<Note, 'id' | 'createdAt'>> }) => 
      noteService.updateNote(id, updates),
    onSuccess: (updatedNote) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.notes.all });
      queryClient.setQueryData(queryKeys.notes.detail(updatedNote.id), updatedNote);
    },
  });
}

export function useDeleteNote() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => noteService.deleteNote(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.notes.all });
    },
  });
}

export function useTogglePinNote() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => noteService.togglePinNote(id),
    onSuccess: (updatedNote) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.notes.all });
      queryClient.setQueryData(queryKeys.notes.detail(updatedNote.id), updatedNote);
    },
  });
}

export function useBulkDeleteNotes() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => noteService.bulkDeleteNotes(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.notes.all });
    },
  });
}

export function useBulkDeleteTasks() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => taskService.bulkDeleteTasks(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks.all });
    },
  });
}

export function useBulkDeleteProjects() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => projectService.bulkDeleteProjects(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.projects.all });
    },
  });
}
