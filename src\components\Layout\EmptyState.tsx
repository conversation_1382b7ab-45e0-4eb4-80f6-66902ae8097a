import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { StandardButton } from '@/components/Layout';

interface EmptyStateProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
  };
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  action,
  className,
  size = 'md',
}) => {
  const { isRTL } = useLanguage();

  const sizeClasses = {
    sm: {
      container: 'py-8',
      icon: 'w-12 h-12 mb-4',
      title: 'text-lg',
      description: 'text-sm',
      maxWidth: 'max-w-sm',
    },
    md: {
      container: 'py-16',
      icon: 'w-20 h-20 mb-6',
      title: 'text-xl',
      description: 'text-base',
      maxWidth: 'max-w-md',
    },
    lg: {
      container: 'py-24',
      icon: 'w-24 h-24 mb-8',
      title: 'text-2xl',
      description: 'text-lg',
      maxWidth: 'max-w-lg',
    },
  };

  const currentSize = sizeClasses[size];

  return (
    <div className={cn(
      'text-center',
      currentSize.container,
      className
    )}>
      <div className={cn('mx-auto', currentSize.maxWidth)}>
        <div className={cn(
          'text-muted-foreground/50 mx-auto',
          currentSize.icon
        )}>
          {icon}
        </div>
        
        <h3 className={cn(
          'font-semibold mb-3 text-foreground',
          currentSize.title,
          isRTL && 'text-right'
        )}>
          {title}
        </h3>
        
        <p className={cn(
          'text-muted-foreground mb-8 leading-relaxed',
          currentSize.description,
          isRTL && 'text-right'
        )}>
          {description}
        </p>
        
        {action && (
          <StandardButton
            onClick={action.onClick}
            size="lg"
            icon={action.icon}
            className="bg-zenith-gradient text-white hover:shadow-zenith-lg"
            fullWidthOnMobile
          >
            {action.label}
          </StandardButton>
        )}
      </div>
    </div>
  );
};

export default EmptyState;
