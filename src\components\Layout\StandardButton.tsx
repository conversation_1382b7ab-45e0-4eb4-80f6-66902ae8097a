import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { Button, ButtonProps } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface StandardButtonProps extends ButtonProps {
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  loading?: boolean;
  loadingText?: string;
  responsive?: boolean;
  fullWidthOnMobile?: boolean;
}

const StandardButton: React.FC<StandardButtonProps> = ({
  children,
  icon,
  iconPosition = 'left',
  loading = false,
  loadingText,
  responsive = true,
  fullWidthOnMobile = false,
  className,
  disabled,
  ...props
}) => {
  const { isRTL } = useLanguage();

  const isDisabled = disabled || loading;
  
  const iconElement = loading ? (
    <Loader2 className="w-4 h-4 animate-spin" />
  ) : icon;

  const content = loading && loadingText ? loadingText : children;

  const getIconSpacing = () => {
    if (!iconElement) return '';
    
    const position = isRTL ? (iconPosition === 'left' ? 'right' : 'left') : iconPosition;
    return position === 'left' ? 'mr-2' : 'ml-2';
  };

  const responsiveClasses = responsive ? cn(
    fullWidthOnMobile && 'w-full sm:w-auto'
  ) : '';

  return (
    <Button
      className={cn(
        'transition-all duration-200',
        responsiveClasses,
        className
      )}
      disabled={isDisabled}
      {...props}
    >
      <div className={cn(
        'flex items-center justify-center gap-2',
        isRTL && 'flex-row-reverse'
      )}>
        {iconElement && iconPosition === 'left' && (
          <span className={cn(iconElement && getIconSpacing())}>
            {iconElement}
          </span>
        )}
        <span className="truncate">{content}</span>
        {iconElement && iconPosition === 'right' && (
          <span className={cn(iconElement && getIconSpacing())}>
            {iconElement}
          </span>
        )}
      </div>
    </Button>
  );
};

export default StandardButton;
