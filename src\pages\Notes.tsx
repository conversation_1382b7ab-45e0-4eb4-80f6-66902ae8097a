import React from 'react';
import NotesList from '@/components/Notes/NotesList';
import { useLanguage } from '@/contexts/LanguageContext';
import { MainLayout, ContentContainer, PageHeader, PageTransition } from '@/components/Layout';

const Notes: React.FC = () => {
  const { t } = useLanguage();

  return (
    <MainLayout>
      <PageTransition type="slide" direction="up">
        <ContentContainer>
          <PageHeader
            title={t('notes.title')}
            description={t('notes.description')}
          />
          <NotesList />
        </ContentContainer>
      </PageTransition>
    </MainLayout>
  );
};

export default Notes;
