import React from 'react';
import SettingsPanel from '@/components/Settings/SettingsPanel';
import { useLanguage } from '@/contexts/LanguageContext';
import { MainLayout, ContentContainer, PageHeader, PageTransition } from '@/components/Layout';

const Settings: React.FC = () => {
  const { t } = useLanguage();

  return (
    <MainLayout>
      <PageTransition type="slide" direction="up">
        <ContentContainer>
          <PageHeader
            title={t('settings.title')}
            description={t('settings.description')}
          />
          <SettingsPanel />
        </ContentContainer>
      </PageTransition>
    </MainLayout>
  );
};

export default Settings;
