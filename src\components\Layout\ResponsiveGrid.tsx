import React from 'react';
import { cn } from '@/lib/utils';
import { gridConfigs, spacing, getResponsiveClasses } from '@/lib/responsive';

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  } | keyof typeof gridConfigs;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  autoFit?: boolean;
  minItemWidth?: string;
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className,
  cols = 'cards',
  gap = 'md',
  autoFit = false,
  minItemWidth = '280px',
}) => {
  const gapClasses = spacing.gap;

  const getGridColsClass = () => {
    if (autoFit) {
      return `grid-cols-[repeat(auto-fit,minmax(${minItemWidth},1fr))]`;
    }

    // If cols is a string, use predefined config
    if (typeof cols === 'string') {
      return getResponsiveClasses.gridCols(cols);
    }

    // If cols is an object, build classes manually
    const classes = [];

    if (cols.default) classes.push(`grid-cols-${cols.default}`);
    if (cols.sm) classes.push(`sm:grid-cols-${cols.sm}`);
    if (cols.md) classes.push(`md:grid-cols-${cols.md}`);
    if (cols.lg) classes.push(`lg:grid-cols-${cols.lg}`);
    if (cols.xl) classes.push(`xl:grid-cols-${cols.xl}`);
    if (cols['2xl']) classes.push(`2xl:grid-cols-${cols['2xl']}`);

    return classes.join(' ');
  };

  return (
    <div className={cn(
      'grid',
      getGridColsClass(),
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
};

export default ResponsiveGrid;
