import { db } from '@/lib/database';
import type { Task, Project, Note } from '@/types/database';
import { taskService } from './taskService';
import { projectService } from './projectService';
import { noteService } from './noteService';

// OpenRouter API configuration
const OPENROUTER_API_KEY = import.meta.env.VITE_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
const GEMINI_MODEL = 'google/gemini-2.5-flash-exp';

export interface AICommand {
  type: 'create' | 'update' | 'delete' | 'search' | 'analyze' | 'suggest';
  entity: 'task' | 'project' | 'note' | 'general';
  data?: Record<string, unknown>;
  query?: string;
}

export interface AIResponse {
  success: boolean;
  message: string;
  data?: unknown;
  suggestions?: string[];
}

export class AIService {
  // Call OpenRouter API with Gemini Flash 2.5
  private async callOpenRouter(messages: Array<{role: string, content: string}>): Promise<string> {
    if (!OPENROUTER_API_KEY) {
      throw new Error('OpenRouter API key not configured');
    }

    try {
      const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Zenith Pulse Manager',
        },
        body: JSON.stringify({
          model: GEMINI_MODEL,
          messages,
          temperature: 0.7,
          max_tokens: 1000,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0]?.message?.content || 'No response from AI';
    } catch (error) {
      console.error('OpenRouter API call failed:', error);
      throw new Error('Failed to get AI response');
    }
  }

  // Process natural language commands with AI
  async processCommand(input: string): Promise<AIResponse> {
    try {
      // First, try to understand the command using AI
      const aiCommand = await this.parseCommandWithAI(input);

      switch (aiCommand.type) {
        case 'create':
          return await this.handleCreate(aiCommand);
        case 'update':
          return await this.handleUpdate(aiCommand);
        case 'delete':
          return await this.handleDelete(aiCommand);
        case 'search':
          return await this.handleSearch(aiCommand);
        case 'analyze':
          return await this.handleAnalyze(aiCommand);
        case 'suggest':
          return await this.handleSuggest(aiCommand);
        default:
          return {
            success: false,
            message: 'I didn\'t understand that command. Try asking me to create, update, search, or analyze your data.',
            suggestions: [
              'Create a new task',
              'Show me my projects',
              'Search for notes about meetings',
              'Analyze my productivity'
            ]
          };
      }
    } catch (error) {
      console.error('AI Service Error:', error);
      return {
        success: false,
        message: 'Sorry, I encountered an error processing your request.',
        suggestions: ['Try rephrasing your request', 'Check if the data exists']
      };
    }
  }

  // Parse command using AI to understand natural language
  private async parseCommandWithAI(input: string): Promise<AICommand> {
    try {
      const systemPrompt = `You are an AI assistant for a task management application. Parse the user's natural language input into a structured command.

Available commands:
- create: Create new tasks, projects, or notes
- update: Update existing items
- delete: Delete items
- search: Search for items
- analyze: Analyze productivity or data
- suggest: Provide suggestions

Available entities: task, project, note, general

Respond with a JSON object containing:
{
  "type": "create|update|delete|search|analyze|suggest",
  "entity": "task|project|note|general",
  "data": {...extracted data...},
  "query": "search query if applicable"
}

Examples:
- "Create a task to review the quarterly report" -> {"type": "create", "entity": "task", "data": {"title": "Review quarterly report"}}
- "Show me all my projects" -> {"type": "search", "entity": "project", "query": "all projects"}
- "Delete the meeting notes" -> {"type": "delete", "entity": "note", "query": "meeting notes"}`;

      const response = await this.callOpenRouter([
        { role: 'system', content: systemPrompt },
        { role: 'user', content: input }
      ]);

      try {
        const parsed = JSON.parse(response);
        return {
          type: parsed.type || 'suggest',
          entity: parsed.entity || 'general',
          data: parsed.data || {},
          query: parsed.query || input
        };
      } catch (parseError) {
        // Fallback to simple parsing if AI response isn't valid JSON
        return this.parseCommand(input);
      }
    } catch (error) {
      console.error('AI command parsing failed:', error);
      // Fallback to simple parsing
      return this.parseCommand(input);
    }
  }

  // Parse natural language input into structured commands
  private parseCommand(input: string): AICommand {
    const lowerInput = input.toLowerCase().trim();
    
    // Create commands
    if (lowerInput.includes('create') || lowerInput.includes('add') || lowerInput.includes('new')) {
      if (lowerInput.includes('task')) {
        return { type: 'create', entity: 'task', data: this.extractTaskData(input) };
      } else if (lowerInput.includes('project')) {
        return { type: 'create', entity: 'project', data: this.extractProjectData(input) };
      } else if (lowerInput.includes('note')) {
        return { type: 'create', entity: 'note', data: this.extractNoteData(input) };
      }
    }
    
    // Search commands
    if (lowerInput.includes('search') || lowerInput.includes('find') || lowerInput.includes('show')) {
      if (lowerInput.includes('task')) {
        return { type: 'search', entity: 'task', query: input };
      } else if (lowerInput.includes('project')) {
        return { type: 'search', entity: 'project', query: input };
      } else if (lowerInput.includes('note')) {
        return { type: 'search', entity: 'note', query: input };
      }
      return { type: 'search', entity: 'general', query: input };
    }
    
    // Update commands
    if (lowerInput.includes('update') || lowerInput.includes('modify') || lowerInput.includes('change')) {
      if (lowerInput.includes('task')) {
        return { type: 'update', entity: 'task', data: this.extractUpdateData(input) };
      } else if (lowerInput.includes('project')) {
        return { type: 'update', entity: 'project', data: this.extractUpdateData(input) };
      }
    }
    
    // Analysis commands
    if (lowerInput.includes('analyze') || lowerInput.includes('report') || lowerInput.includes('summary')) {
      return { type: 'analyze', entity: 'general', query: input };
    }
    
    // Suggestion commands
    if (lowerInput.includes('suggest') || lowerInput.includes('recommend') || lowerInput.includes('help')) {
      return { type: 'suggest', entity: 'general', query: input };
    }
    
    // Default to search
    return { type: 'search', entity: 'general', query: input };
  }

  // Handle create commands
  private async handleCreate(command: AICommand): Promise<AIResponse> {
    try {
      switch (command.entity) {
        case 'task': {
          const taskData = command.data as Partial<Task>;
          const task = await taskService.createTask({
            title: taskData.title || 'New Task',
            description: taskData.description,
            priority: taskData.priority || 'medium',
            status: 'todo',
            tags: taskData.tags || [],
            order: 0
          });
          return {
            success: true,
            message: `Created task: ${task.title}`,
            data: task
          };
        }

        case 'project': {
          const projectData = command.data as Partial<Project>;
          const project = await projectService.createProject({
            name: projectData.name || 'New Project',
            description: projectData.description,
            status: 'planning',
            progress: 0,
            color: projectData.color || '#3b82f6',
            teamMembers: projectData.teamMembers || [],
            tags: projectData.tags || [],
            priority: projectData.priority || 'medium'
          });
          return {
            success: true,
            message: `Created project: ${project.name}`,
            data: project
          };
        }

        case 'note': {
          const noteData = command.data as Partial<Note>;
          const note = await noteService.createNote({
            title: noteData.title || 'New Note',
            content: noteData.content || '',
            tags: noteData.tags || [],
            isPinned: false,
            isArchived: false,
            attachments: []
          });
          return {
            success: true,
            message: `Created note: ${note.title}`,
            data: note
          };
        }

        default:
          return {
            success: false,
            message: 'Please specify what you want to create (task, project, or note).'
          };
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to create ${command.entity}: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Handle search commands
  private async handleSearch(command: AICommand): Promise<AIResponse> {
    try {
      const query = command.query || '';
      const searchTerm = this.extractSearchTerm(query);
      
      if (command.entity === 'general') {
        // Search across all entities
        const results = await db.searchEntities(searchTerm);
        return {
          success: true,
          message: `Found ${results.length} results for "${searchTerm}"`,
          data: results
        };
      } else {
        // Entity-specific search
        let results: unknown[] = [];
        switch (command.entity) {
          case 'task':
            results = await taskService.searchTasks(searchTerm);
            break;
          case 'project':
            results = await projectService.searchProjects(searchTerm);
            break;
          case 'note':
            results = await noteService.searchNotes(searchTerm);
            break;
        }
        
        return {
          success: true,
          message: `Found ${results.length} ${command.entity}(s) for "${searchTerm}"`,
          data: results
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Handle analysis commands
  private async handleAnalyze(command: AICommand): Promise<AIResponse> {
    try {
      const [taskStats, projectStats, noteStats] = await Promise.all([
        taskService.getTaskStats(),
        projectService.getProjectStats(),
        noteService.getNoteStats()
      ]);

      const analysis = {
        productivity: {
          completionRate: taskStats.total > 0 ? Math.round((taskStats.completed / taskStats.total) * 100) : 0,
          totalTasks: taskStats.total,
          completedTasks: taskStats.completed,
          overdueTasks: taskStats.overdue
        },
        projects: {
          total: projectStats.total,
          active: projectStats.active,
          completed: projectStats.completed
        },
        notes: {
          total: noteStats.total,
          totalWords: noteStats.totalWords,
          averageWords: noteStats.averageWords
        }
      };

      let message = `Here's your productivity analysis:\n\n`;
      message += `📊 Tasks: ${taskStats.completed}/${taskStats.total} completed (${analysis.productivity.completionRate}%)\n`;
      message += `🚀 Projects: ${projectStats.active} active, ${projectStats.completed} completed\n`;
      message += `📝 Notes: ${noteStats.total} notes with ${noteStats.totalWords} total words\n`;
      
      if (taskStats.overdue > 0) {
        message += `⚠️ You have ${taskStats.overdue} overdue tasks that need attention.`;
      }

      return {
        success: true,
        message,
        data: analysis
      };
    } catch (error) {
      return {
        success: false,
        message: `Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Handle suggestion commands
  private async handleSuggest(command: AICommand): Promise<AIResponse> {
    try {
      const suggestions = await this.generateSuggestions();
      
      return {
        success: true,
        message: 'Here are some suggestions to improve your productivity:',
        suggestions
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to generate suggestions: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Handle update commands
  private async handleUpdate(command: AICommand): Promise<AIResponse> {
    // This would require more complex parsing to identify which item to update
    return {
      success: false,
      message: 'Update functionality is not yet implemented. Please use the interface to update items.',
      suggestions: ['Use the edit buttons in the interface', 'Try creating a new item instead']
    };
  }

  // Handle delete commands
  private async handleDelete(command: AICommand): Promise<AIResponse> {
    // This would require more complex parsing and confirmation
    return {
      success: false,
      message: 'Delete functionality is not yet implemented for safety. Please use the interface to delete items.',
      suggestions: ['Use the delete buttons in the interface', 'Archive items instead of deleting']
    };
  }

  // Extract task data from natural language
  private extractTaskData(input: string): Partial<Task> {
    const data: Partial<Task> = {};
    
    // Extract title (everything after "task" and before priority/due date keywords)
    const titleMatch = input.match(/(?:task|add|create)\s+["']?([^"']+?)["']?(?:\s+(?:with|priority|due|for)|$)/i);
    if (titleMatch) {
      data.title = titleMatch[1].trim();
    }
    
    // Extract priority
    if (input.toLowerCase().includes('high priority') || input.toLowerCase().includes('urgent')) {
      data.priority = 'high';
    } else if (input.toLowerCase().includes('low priority')) {
      data.priority = 'low';
    } else {
      data.priority = 'medium';
    }
    
    // Extract tags
    const tagMatch = input.match(/(?:tag|tags|tagged)\s+([^.]+)/i);
    if (tagMatch) {
      data.tags = tagMatch[1].split(',').map(tag => tag.trim());
    }
    
    return data;
  }

  // Extract project data from natural language
  private extractProjectData(input: string): Partial<Project> {
    const data: Partial<Project> = {};
    
    const nameMatch = input.match(/(?:project|add|create)\s+["']?([^"']+?)["']?(?:\s+(?:with|for)|$)/i);
    if (nameMatch) {
      data.name = nameMatch[1].trim();
    }
    
    return data;
  }

  // Extract note data from natural language
  private extractNoteData(input: string): Partial<Note> {
    const data: Partial<Note> = {};
    
    const titleMatch = input.match(/(?:note|add|create)\s+["']?([^"']+?)["']?(?:\s+(?:with|about)|$)/i);
    if (titleMatch) {
      data.title = titleMatch[1].trim();
    }
    
    return data;
  }

  // Extract search term from query
  private extractSearchTerm(query: string): string {
    // Remove command words and extract the actual search term
    const cleanQuery = query
      .replace(/(?:search|find|show|for|me|my)\s+/gi, '')
      .replace(/(?:task|project|note)s?\s+/gi, '')
      .trim();
    
    return cleanQuery || query;
  }

  // Extract update data from natural language
  private extractUpdateData(input: string): Record<string, unknown> {
    // This would need more sophisticated NLP to extract what to update
    return {};
  }

  // Generate productivity suggestions
  private async generateSuggestions(): Promise<string[]> {
    const [taskStats, overdueTasks] = await Promise.all([
      taskService.getTaskStats(),
      taskService.getOverdueTasks()
    ]);

    const suggestions: string[] = [];

    if (overdueTasks.length > 0) {
      suggestions.push(`Focus on ${overdueTasks.length} overdue tasks first`);
    }

    if (taskStats.inProgress > 5) {
      suggestions.push('Consider completing some in-progress tasks before starting new ones');
    }

    if (taskStats.completed === 0 && taskStats.total > 0) {
      suggestions.push('Start with your highest priority task to build momentum');
    }

    suggestions.push('Take regular breaks using the Focus Mode feature');
    suggestions.push('Review and organize your notes weekly');
    suggestions.push('Set realistic deadlines for your tasks');

    return suggestions;
  }
}

// Export singleton instance
export const aiService = new AIService();
