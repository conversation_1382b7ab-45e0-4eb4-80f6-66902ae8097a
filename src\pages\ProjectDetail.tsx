import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit, Trash2, Folder, Calendar, Users, Target, DollarSign, MoreVertical, Plus } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useProject, useDeleteProject, useTasksByProject } from '@/hooks/useData';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import ProjectForm from '@/components/Projects/ProjectForm';
import TaskForm from '@/components/Tasks/TaskForm';

const ProjectDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t, isRTL } = useLanguage();
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  const [isTaskFormOpen, setIsTaskFormOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const { data: project, isLoading, error } = useProject(id!);
  const { data: tasks = [] } = useTasksByProject(id!);
  const deleteProjectMutation = useDeleteProject();

  const handleDelete = async () => {
    if (!project) return;
    try {
      await deleteProjectMutation.mutateAsync(project.id);
      navigate('/', { state: { activeSection: 'projects' } });
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting project:', error);
    }
  };

  const openDeleteDialog = () => {
    setDeleteDialogOpen(true);
  };

  const formatDate = (dateString: string | Date) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-zenith-success-100 text-zenith-success-800 dark:bg-zenith-success-900/20 dark:text-zenith-success-400';
      case 'completed': return 'bg-zenith-primary-100 text-zenith-primary-800 dark:bg-zenith-primary-900/20 dark:text-zenith-primary-400';
      case 'onHold': return 'bg-zenith-warning-100 text-zenith-warning-800 dark:bg-zenith-warning-900/20 dark:text-zenith-warning-400';
      case 'planning': return 'bg-zenith-secondary-100 text-zenith-secondary-800 dark:bg-zenith-secondary-900/20 dark:text-zenith-secondary-400';
      case 'cancelled': return 'bg-zenith-error-100 text-zenith-error-800 dark:bg-zenith-error-900/20 dark:text-zenith-error-400';
      default: return 'bg-zenith-neutral-100 text-zenith-neutral-800 dark:bg-zenith-neutral-900/20 dark:text-zenith-neutral-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-zenith-error-100 text-zenith-error-800 dark:bg-zenith-error-900/20 dark:text-zenith-error-400';
      case 'medium': return 'bg-zenith-warning-100 text-zenith-warning-800 dark:bg-zenith-warning-900/20 dark:text-zenith-warning-400';
      case 'low': return 'bg-zenith-success-100 text-zenith-success-800 dark:bg-zenith-success-900/20 dark:text-zenith-success-400';
      default: return 'bg-zenith-neutral-100 text-zenith-neutral-800 dark:bg-zenith-neutral-900/20 dark:text-zenith-neutral-400';
    }
  };

  const completedTasks = tasks.filter(task => task.status === 'completed').length;
  const totalTasks = tasks.length;

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
        <p className="text-muted-foreground">{t('common.loading')}</p>
      </div>
    );
  }

  if (error) {
    console.error('Project detail error:', error);
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Folder className="w-16 h-16 text-destructive mb-4" />
        <h2 className="text-2xl font-semibold mb-2 text-destructive">{t('common.error')}</h2>
        <p className="text-muted-foreground mb-4 text-center max-w-md">
          {error instanceof Error ? error.message : t('projects.loadError')}
        </p>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            {t('common.retry')}
          </Button>
          <Button onClick={() => navigate('/', { state: { activeSection: 'projects' } })}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('common.back')}
          </Button>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Folder className="w-16 h-16 text-muted-foreground mb-4" />
        <h2 className="text-2xl font-semibold mb-2">{t('projects.notFound')}</h2>
        <p className="text-muted-foreground mb-4">{t('projects.notFoundDescription')}</p>
        <Button onClick={() => navigate('/', { state: { activeSection: 'projects' } })}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          {t('common.back')}
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-6xl mx-auto p-6">
        {/* Header */}
        <div className={cn("flex items-center justify-between mb-6", isRTL && "flex-row-reverse")}>
          <div className={cn("flex items-center gap-4", isRTL && "flex-row-reverse")}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/', { state: { activeSection: 'projects' } })}
              className="p-2"
            >
              <ArrowLeft className={cn("w-5 h-5", isRTL && "rotate-180")} />
            </Button>
            <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
              <Folder className="w-6 h-6 text-primary" />
              <div>
                <div className={cn("flex items-center gap-2 text-sm text-muted-foreground mb-1", isRTL && "flex-row-reverse")}>
                  <span className="hover:text-primary cursor-pointer" onClick={() => navigate('/', { state: { activeSection: 'projects' } })}>
                    {t('projects.title')}
                  </span>
                  <span>/</span>
                  <span>{project.name}</span>
                </div>
                <h1 className="text-2xl font-bold">{t('projects.projectDetails')}</h1>
              </div>
            </div>
          </div>

          <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
            <Button
              variant="ghost"
              size="sm"
              className="text-destructive hover:text-destructive hover:bg-destructive/10"
              onClick={openDeleteDialog}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {t('projects.delete')}
            </Button>
            <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical className="w-5 h-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align={isRTL ? "start" : "end"}>
              <DropdownMenuItem onClick={() => setIsEditFormOpen(true)}>
                <Edit className="w-4 h-4 mr-2" />
                {t('projects.edit')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={openDeleteDialog}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                {t('projects.delete')}
              </DropdownMenuItem>
            </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Project Info */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Main Info */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className={cn("flex items-start justify-between", isRTL && "flex-row-reverse")}>
                  <div className="flex-1">
                    <CardTitle className="text-2xl mb-2">{project.name}</CardTitle>
                    {project.description && (
                      <p className="text-muted-foreground">{project.description}</p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Badge className={cn("text-sm", getStatusColor(project.status))}>
                      {t(`projects.status.${project.status}`)}
                    </Badge>
                    <Badge className={cn("text-sm", getPriorityColor(project.priority))}>
                      {t(`projects.priority.${project.priority}`)}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Progress */}
                <div>
                  <div className={cn("flex items-center justify-between mb-2", isRTL && "flex-row-reverse")}>
                    <span className="text-sm font-medium">{t('projects.progress')}</span>
                    <span className="text-sm font-bold">{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="h-3" />
                </div>

                {/* Tags */}
                {project.tags.length > 0 && (
                  <div>
                    <div className={cn("flex items-center gap-2 mb-3", isRTL && "flex-row-reverse")}>
                      <Target className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">{t('projects.tags')}</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {project.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-sm">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Team Members */}
                {project.teamMembers.length > 0 && (
                  <div>
                    <div className={cn("flex items-center gap-2 mb-3", isRTL && "flex-row-reverse")}>
                      <Users className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">{t('projects.teamMembers')}</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {project.teamMembers.map((member, index) => (
                        <Badge key={index} variant="secondary" className="text-sm">
                          {member}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Side Info */}
          <div className="space-y-6">
            {/* Stats Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('projects.statistics')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">{t('projects.totalTasks')}</span>
                  <span className="font-semibold">{totalTasks}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">{t('projects.completedTasks')}</span>
                  <span className="font-semibold">{completedTasks}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">{t('projects.remainingTasks')}</span>
                  <span className="font-semibold">{totalTasks - completedTasks}</span>
                </div>
                {project.budget && (
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">{t('projects.budget')}</span>
                    <span className="font-semibold">${project.budget.toLocaleString()}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Dates Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('projects.dates')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {project.startDate && (
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">{t('projects.startDate')}</span>
                    <span className="text-sm">{formatDate(project.startDate)}</span>
                  </div>
                )}
                {project.endDate && (
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">{t('projects.endDate')}</span>
                    <span className="text-sm">{formatDate(project.endDate)}</span>
                  </div>
                )}
                {project.deadline && (
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">{t('projects.deadline')}</span>
                    <span className="text-sm">{formatDate(project.deadline)}</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">{t('projects.created')}</span>
                  <span className="text-sm">{formatDate(project.createdAt)}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Tasks Section */}
        <Card>
          <CardHeader>
            <div className={cn("flex items-center justify-between", isRTL && "flex-row-reverse")}>
              <CardTitle className="text-xl">{t('projects.tasks')} ({totalTasks})</CardTitle>
              <Button onClick={() => setIsTaskFormOpen(true)} size="sm">
                <Plus className="w-4 h-4 mr-2" />
                {t('tasks.newTask')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {tasks.length === 0 ? (
              <div className="text-center py-8">
                <Target className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">{t('tasks.noTasks')}</h3>
                <p className="text-muted-foreground mb-4">{t('tasks.noTasksDescription')}</p>
                <Button onClick={() => setIsTaskFormOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  {t('tasks.newTask')}
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                {tasks.map((task) => (
                  <div
                    key={task.id}
                    className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                    onClick={() => navigate(`/tasks/${task.id}`)}
                  >
                    <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
                      <div className={cn(
                        "w-3 h-3 rounded-full",
                        task.status === 'completed' ? 'bg-green-500' :
                        task.status === 'inProgress' ? 'bg-blue-500' :
                        task.status === 'cancelled' ? 'bg-red-500' : 'bg-gray-400'
                      )} />
                      <div>
                        <h4 className="font-medium">{task.title}</h4>
                        {task.description && (
                          <p className="text-sm text-muted-foreground line-clamp-1">{task.description}</p>
                        )}
                      </div>
                    </div>
                    <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                      <Badge variant="outline" className="text-xs">
                        {t(`tasks.priority.${task.priority}`)}
                      </Badge>
                      {task.dueDate && (
                        <span className="text-xs text-muted-foreground">
                          {formatDate(task.dueDate)}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Forms */}
        <ProjectForm 
          isOpen={isEditFormOpen} 
          onClose={() => setIsEditFormOpen(false)}
          project={project}
        />
        
        <TaskForm
          isOpen={isTaskFormOpen}
          onClose={() => setIsTaskFormOpen(false)}
          projectId={project.id}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{t('projects.confirmDelete')}</AlertDialogTitle>
              <AlertDialogDescription>
                {t('projects.confirmDeleteDescription')}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)}>
                {t('common.cancel')}
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {t('projects.delete')}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default ProjectDetail;
