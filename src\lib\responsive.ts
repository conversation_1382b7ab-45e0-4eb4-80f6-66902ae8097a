/**
 * Responsive Design System
 * 
 * This file defines the standardized responsive breakpoints and utilities
 * used throughout the application to ensure consistent behavior across
 * all screen sizes.
 */

// Standard breakpoints following Tailwind CSS mobile-first approach
export const breakpoints = {
  // Mobile devices (default, no prefix needed)
  mobile: '0px',
  
  // Small tablets and large phones
  sm: '640px',
  
  // Tablets and small laptops
  md: '768px',
  
  // Laptops and desktops
  lg: '1024px',
  
  // Large desktops
  xl: '1280px',
  
  // Extra large screens
  '2xl': '1536px',
} as const;

// Grid column configurations for different screen sizes
export const gridConfigs = {
  // Standard content grids
  content: {
    default: 1,
    sm: 2,
    lg: 3,
    xl: 4,
  },
  
  // Dashboard/analytics grids
  dashboard: {
    default: 1,
    md: 2,
    lg: 4,
  },
  
  // Card grids for projects, notes, tasks
  cards: {
    default: 1,
    sm: 2,
    lg: 3,
    xl: 4,
    '2xl': 5,
  },
  
  // Stats/metrics grids
  stats: {
    default: 1,
    sm: 2,
    lg: 4,
  },
  
  // Form grids
  form: {
    default: 1,
    lg: 2,
  },
} as const;

// Spacing configurations for different screen sizes
export const spacing = {
  // Container padding
  container: {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-6 lg:p-8',
    xl: 'p-8 lg:p-10',
  },

  // Section spacing
  section: {
    none: '',
    sm: 'space-y-4',
    md: 'space-y-6',
    lg: 'space-y-8',
    xl: 'space-y-10',
  },

  // Grid gaps
  gap: {
    sm: 'gap-3',
    md: 'gap-4 sm:gap-6',
    lg: 'gap-6 sm:gap-8',
    xl: 'gap-8 sm:gap-10',
  },

  // Margin spacing
  margin: {
    sm: 'm-4',
    md: 'm-6',
    lg: 'm-6 lg:m-8',
    xl: 'm-8 lg:m-10',
  },

  // Padding spacing
  padding: {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-6 lg:p-8',
    xl: 'p-8 lg:p-10',
  },
} as const;

// Typography scales for responsive design
export const typography = {
  // Page titles
  pageTitle: {
    mobile: 'text-2xl',
    sm: 'text-3xl',
    lg: 'text-4xl',
  },
  
  // Section headings
  sectionTitle: {
    mobile: 'text-lg',
    sm: 'text-xl',
    lg: 'text-2xl',
  },
  
  // Card titles
  cardTitle: {
    mobile: 'text-base',
    sm: 'text-lg',
  },
  
  // Body text
  body: {
    mobile: 'text-sm',
    sm: 'text-base',
  },
  
  // Descriptions
  description: {
    mobile: 'text-sm',
    sm: 'text-base',
    lg: 'text-lg',
  },
} as const;

// Modal/Dialog responsive configurations
export const modal = {
  // Modal sizes for different screens
  sizes: {
    sm: {
      mobile: 'max-w-sm mx-2',
      sm: 'max-w-sm',
    },
    md: {
      mobile: 'max-w-md mx-2',
      sm: 'max-w-md',
    },
    lg: {
      mobile: 'max-w-lg mx-2',
      sm: 'max-w-2xl',
    },
    xl: {
      mobile: 'max-w-xl mx-2',
      sm: 'max-w-4xl',
    },
  },
  
  // Modal heights
  heights: {
    mobile: 'max-h-[90vh]',
    sm: 'max-h-[85vh]',
  },
  
  // Modal padding
  padding: {
    mobile: 'p-4',
    sm: 'p-6',
  },
} as const;

// Button responsive configurations
export const button = {
  // Button sizes
  sizes: {
    sm: {
      mobile: 'px-3 py-2 text-sm',
      sm: 'px-4 py-2 text-sm',
    },
    md: {
      mobile: 'px-4 py-2',
      sm: 'px-6 py-2',
    },
    lg: {
      mobile: 'px-6 py-3',
      sm: 'px-8 py-3',
    },
  },
  
  // Full width on mobile option
  fullWidthMobile: 'w-full sm:w-auto',
} as const;

// Utility functions for responsive classes
export const getResponsiveClasses = {
  // Get grid column classes
  gridCols: (config: keyof typeof gridConfigs) => {
    const cols = gridConfigs[config];
    const classes = [];
    
    if (cols.default) classes.push(`grid-cols-${cols.default}`);
    if (cols.sm) classes.push(`sm:grid-cols-${cols.sm}`);
    if (cols.md) classes.push(`md:grid-cols-${cols.md}`);
    if (cols.lg) classes.push(`lg:grid-cols-${cols.lg}`);
    if (cols.xl) classes.push(`xl:grid-cols-${cols.xl}`);
    if (cols['2xl']) classes.push(`2xl:grid-cols-${cols['2xl']}`);
    
    return classes.join(' ');
  },
  
  // Get typography classes
  typography: (config: keyof typeof typography) => {
    const typo = typography[config];
    const classes = [];
    
    if (typo.mobile) classes.push(typo.mobile);
    if (typo.sm) classes.push(`sm:${typo.sm}`);
    if (typo.md) classes.push(`md:${typo.md}`);
    if (typo.lg) classes.push(`lg:${typo.lg}`);
    if (typo.xl) classes.push(`xl:${typo.xl}`);
    
    return classes.join(' ');
  },
  
  // Get spacing classes
  spacing: (config: keyof typeof spacing) => {
    const space = spacing[config];
    const classes = [];
    
    if (space.mobile) classes.push(space.mobile);
    if (space.sm) classes.push(`sm:${space.sm}`);
    if (space.md) classes.push(`md:${space.md}`);
    if (space.lg) classes.push(`lg:${space.lg}`);
    if (space.xl) classes.push(`xl:${space.xl}`);
    
    return classes.join(' ');
  },
} as const;

export default {
  breakpoints,
  gridConfigs,
  spacing,
  typography,
  modal,
  button,
  getResponsiveClasses,
};
