import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
  showBackground?: boolean;
  fullHeight?: boolean;
}

/**
 * MainLayout - Layout wrapper for main application content
 * Provides consistent background, spacing, and responsive behavior
 */
const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  className,
  showBackground = true,
  fullHeight = true,
}) => {
  const { isRTL } = useLanguage();

  return (
    <div 
      className={cn(
        "transition-colors duration-200",
        showBackground && "bg-background",
        fullHeight && "min-h-screen",
        isRTL ? "rtl" : "ltr",
        className
      )} 
      dir={isRTL ? "rtl" : "ltr"}
    >
      {children}
    </div>
  );
};

export default MainLayout;
