import React from 'react';
import { Button } from '@/components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Trash2, X, CheckSquare, Square } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useMultiSelect } from '@/contexts/MultiSelectContext';
import { cn } from '@/lib/utils';

interface MultiSelectToolbarProps {
  onBulkDelete?: (selectedIds: string[]) => Promise<void>;
  onSelectAll?: () => void;
  totalItems?: number;
  className?: string;
  deleteConfirmTitle?: string;
  deleteConfirmDescription?: string;
}

export const MultiSelectToolbar: React.FC<MultiSelectToolbarProps> = ({
  onBulkDelete,
  onSelectAll,
  totalItems = 0,
  className,
  deleteConfirmTitle,
  deleteConfirmDescription,
}) => {
  const { t, isRTL } = useLanguage();
  const { 
    selectedCount, 
    clearSelection, 
    toggleSelectionMode, 
    isSelectionMode,
    selectedItems 
  } = useMultiSelect();

  const handleBulkDelete = async () => {
    if (onBulkDelete && selectedItems.size > 0) {
      try {
        await onBulkDelete(Array.from(selectedItems));
        clearSelection();
      } catch (error) {
        console.error('Error in bulk delete:', error);
      }
    }
  };

  const isAllSelected = totalItems > 0 && selectedCount === totalItems;

  if (!isSelectionMode) {
    return null;
  }

  return (
    <div className={cn(
      "fixed top-20 left-0 right-0 z-50 bg-background border-b border-border shadow-lg",
      "transition-all duration-200 ease-in-out",
      className
    )}>
      <div className="container mx-auto px-4 py-3">
        <div className={cn(
          "flex items-center justify-between",
          isRTL && "flex-row-reverse"
        )}>
          <div className={cn(
            "flex items-center gap-4",
            isRTL && "flex-row-reverse"
          )}>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSelectionMode}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="w-4 h-4" />
            </Button>
            
            <span className="text-sm font-medium">
              {selectedCount > 0 
                ? t('common.selectedItems', { count: selectedCount })
                : t('common.selectItems')
              }
            </span>
          </div>

          <div className={cn(
            "flex items-center gap-2",
            isRTL && "flex-row-reverse"
          )}>
            {onSelectAll && (
              <Button
                variant="outline"
                size="sm"
                onClick={onSelectAll}
                className="text-sm"
              >
                {isAllSelected ? (
                  <>
                    <Square className="w-4 h-4 mr-2" />
                    {t('common.deselectAll')}
                  </>
                ) : (
                  <>
                    <CheckSquare className="w-4 h-4 mr-2" />
                    {t('common.selectAll')}
                  </>
                )}
              </Button>
            )}

            {onBulkDelete && selectedCount > 0 && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="destructive"
                    size="sm"
                    className="text-sm"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    {t('common.deleteSelected')}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      {deleteConfirmTitle || t('common.confirmBulkDelete')}
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      {deleteConfirmDescription || 
                        t('common.confirmBulkDeleteDescription', { count: selectedCount })
                      }
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleBulkDelete}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      {t('common.delete')}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiSelectToolbar;
