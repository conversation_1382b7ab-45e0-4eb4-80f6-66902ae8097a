# Zenith Pulse Manager Environment Variables

# OpenRouter API Configuration
# Get your API key from: https://openrouter.ai/settings/credits
VITE_OPENROUTER_API_KEY=your_openrouter_api_key_here

# Application Configuration
VITE_APP_NAME="Zenith Pulse Manager"
VITE_APP_VERSION="1.0.0"

# Development Configuration
VITE_DEV_MODE=true
VITE_DEBUG_MODE=false

# Database Configuration (IndexedDB - no configuration needed)
# The app uses IndexedDB for offline-first data storage

# Theme Configuration
VITE_DEFAULT_THEME=system
VITE_DEFAULT_LANGUAGE=en

# Feature Flags
VITE_ENABLE_AI_ASSISTANT=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_FOCUS_MODE=true
