import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface MultiSelectContextType {
  selectedItems: Set<string>;
  isSelectionMode: boolean;
  selectItem: (id: string) => void;
  deselectItem: (id: string) => void;
  toggleItem: (id: string) => void;
  selectAll: (ids: string[]) => void;
  clearSelection: () => void;
  toggleSelectionMode: () => void;
  isSelected: (id: string) => boolean;
  selectedCount: number;
}

const MultiSelectContext = createContext<MultiSelectContextType | undefined>(undefined);

interface MultiSelectProviderProps {
  children: ReactNode;
  onSelectionChange?: (selectedIds: string[]) => void;
}

export const MultiSelectProvider: React.FC<MultiSelectProviderProps> = ({ 
  children, 
  onSelectionChange 
}) => {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  const selectItem = useCallback((id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      newSet.add(id);
      onSelectionChange?.(Array.from(newSet));
      return newSet;
    });
  }, [onSelectionChange]);

  const deselectItem = useCallback((id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      onSelectionChange?.(Array.from(newSet));
      return newSet;
    });
  }, [onSelectionChange]);

  const toggleItem = useCallback((id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      onSelectionChange?.(Array.from(newSet));
      return newSet;
    });
  }, [onSelectionChange]);

  const selectAll = useCallback((ids: string[]) => {
    const newSet = new Set(ids);
    setSelectedItems(newSet);
    onSelectionChange?.(Array.from(newSet));
  }, [onSelectionChange]);

  const clearSelection = useCallback(() => {
    setSelectedItems(new Set());
    onSelectionChange?.([]);
  }, [onSelectionChange]);

  const toggleSelectionMode = useCallback(() => {
    setIsSelectionMode(prev => {
      const newMode = !prev;
      if (!newMode) {
        clearSelection();
      }
      return newMode;
    });
  }, [clearSelection]);

  const isSelected = useCallback((id: string) => {
    return selectedItems.has(id);
  }, [selectedItems]);

  const selectedCount = selectedItems.size;

  const value: MultiSelectContextType = {
    selectedItems,
    isSelectionMode,
    selectItem,
    deselectItem,
    toggleItem,
    selectAll,
    clearSelection,
    toggleSelectionMode,
    isSelected,
    selectedCount,
  };

  return (
    <MultiSelectContext.Provider value={value}>
      {children}
    </MultiSelectContext.Provider>
  );
};

export const useMultiSelect = (): MultiSelectContextType => {
  const context = useContext(MultiSelectContext);
  if (context === undefined) {
    throw new Error('useMultiSelect must be used within a MultiSelectProvider');
  }
  return context;
};
