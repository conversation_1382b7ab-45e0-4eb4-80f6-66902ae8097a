import React, { ReactNode } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { useMultiSelect } from '@/contexts/MultiSelectContext';
import { cn } from '@/lib/utils';

interface SelectableItemProps {
  id: string;
  children: ReactNode;
  className?: string;
  onItemClick?: () => void;
  disabled?: boolean;
}

export const SelectableItem: React.FC<SelectableItemProps> = ({
  id,
  children,
  className,
  onItemClick,
  disabled = false,
}) => {
  const { isSelectionMode, isSelected, toggleItem } = useMultiSelect();

  const handleCheckboxChange = (checked: boolean) => {
    if (!disabled) {
      toggleItem(id);
    }
  };

  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleItemClick = () => {
    if (isSelectionMode && !disabled) {
      toggleItem(id);
    } else if (onItemClick) {
      onItemClick();
    }
  };

  const selected = isSelected(id);

  return (
    <div
      className={cn(
        "relative transition-all duration-200",
        isSelectionMode && "cursor-pointer",
        selected && "ring-2 ring-primary ring-offset-2",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      onClick={handleItemClick}
    >
      {isSelectionMode && (
        <div className="absolute top-2 left-2 z-10" onClick={handleCheckboxClick}>
          <Checkbox
            checked={selected}
            onCheckedChange={handleCheckboxChange}
            disabled={disabled}
            className="bg-background border-2 shadow-sm"
          />
        </div>
      )}
      
      <div className={cn(
        "transition-all duration-200",
        isSelectionMode && "pl-10",
        selected && "opacity-75"
      )}>
        {children}
      </div>
    </div>
  );
};

export default SelectableItem;
