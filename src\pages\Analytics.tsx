import React from 'react';
import AnalyticsOverview from '@/components/Analytics/AnalyticsOverview';
import { useLanguage } from '@/contexts/LanguageContext';
import { MainLayout, ContentContainer, PageHeader, PageTransition } from '@/components/Layout';

const Analytics: React.FC = () => {
  const { t } = useLanguage();

  return (
    <MainLayout>
      <PageTransition type="slide" direction="up">
        <ContentContainer>
          <PageHeader
            title={t('analytics.title')}
            description={t('analytics.description')}
          />
          <AnalyticsOverview />
        </ContentContainer>
      </PageTransition>
    </MainLayout>
  );
};

export default Analytics;
