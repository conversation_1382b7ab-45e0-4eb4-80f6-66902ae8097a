import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

interface ContentContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '4xl' | '5xl' | '6xl' | '7xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  centered?: boolean;
}

/**
 * ContentContainer - Standardized content wrapper
 * Provides consistent max-width, padding, and spacing across all pages
 */
const ContentContainer: React.FC<ContentContainerProps> = ({
  children,
  className,
  maxWidth = '6xl',
  padding = 'lg',
  spacing = 'md',
  centered = true,
}) => {
  const { isRTL } = useLanguage();

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full',
  };

  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-6 lg:p-8',
    xl: 'p-8 lg:p-10',
  };

  const spacingClasses = {
    none: '',
    sm: 'space-y-4',
    md: 'space-y-6',
    lg: 'space-y-8',
    xl: 'space-y-10',
  };

  return (
    <div className={cn(
      maxWidthClasses[maxWidth],
      paddingClasses[padding],
      spacingClasses[spacing],
      centered && 'mx-auto',
      className
    )}>
      {children}
    </div>
  );
};

export default ContentContainer;
