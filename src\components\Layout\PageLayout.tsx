import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { typography, spacing, getResponsiveClasses } from '@/lib/responsive';

interface PageLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '6xl' | '7xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  showBorder?: boolean;
  variant?: 'default' | 'centered' | 'wide';
  headerSpacing?: 'sm' | 'md' | 'lg';
}

const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  title,
  description,
  actions,
  className,
  maxWidth = '6xl',
  padding = 'lg',
  showBorder = true,
  variant = 'default',
  headerSpacing = 'md',
}) => {
  const { t, isRTL } = useLanguage();

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full',
  };

  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-6 lg:p-8',
    xl: 'p-8 lg:p-10',
  };

  const headerSpacingClasses = {
    sm: 'space-y-2',
    md: 'space-y-3',
    lg: 'space-y-4',
  };

  const variantClasses = {
    default: '',
    centered: 'text-center',
    wide: 'max-w-full',
  };

  return (
    <div className={cn(
      "min-h-screen bg-background transition-colors duration-200",
      variant === 'wide' && 'w-full'
    )}>
      <div className={cn(
        'mx-auto',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        variantClasses[variant],
        className
      )}>
        {/* Page Header */}
        <div className={cn(
          headerSpacingClasses[headerSpacing],
          showBorder && 'pb-6 border-b border-border/50',
          isRTL && 'text-right',
          variant === 'centered' && 'text-center'
        )}>
          <div className={cn(
            'flex items-start justify-between gap-4',
            isRTL && 'flex-row-reverse',
            variant === 'centered' && 'flex-col items-center gap-3'
          )}>
            <div className={cn(
              'flex-1 min-w-0',
              variant === 'centered' && 'flex-none text-center'
            )}>
              <h1 className={cn(
                'font-bold text-foreground tracking-tight',
                getResponsiveClasses.typography('pageTitle')
              )}>
                {title}
              </h1>
              {description && (
                <p className={cn(
                  'text-muted-foreground mt-2 leading-relaxed',
                  getResponsiveClasses.typography('description')
                )}>
                  {description}
                </p>
              )}
            </div>
            {actions && (
              <div className={cn(
                'flex-shrink-0',
                variant === 'centered' && 'flex-none'
              )}>
                {actions}
              </div>
            )}
          </div>
        </div>

        {/* Page Content */}
        <div className={cn(
          'mt-6',
          getResponsiveClasses.spacing('section')
        )}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default PageLayout;
