import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Save, Folder, Calendar, Users, Target, DollarSign } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useCreateProject, useUpdateProject } from '@/hooks/useData';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import { ResponsiveModal } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import type { Project } from '@/types/database';

// Form validation schema - will be created with proper translations
const createProjectSchema = (t: (key: string) => string) => z.object({
  name: z.string().min(1, t('projects.validation.nameRequired')).max(100, t('projects.validation.nameTooLong')),
  description: z.string().optional(),
  status: z.enum(['planning', 'active', 'completed', 'cancelled', 'onHold']).default('planning'),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  deadline: z.string().optional(),
  progress: z.number().min(0).max(100).default(0),
  color: z.string().default('#3b82f6'),
  teamMembers: z.array(z.string()).default([]),
  budget: z.number().optional(),
  tags: z.array(z.string()).default([]),
  clientName: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']).default('medium')
});

type ProjectFormData = z.infer<ReturnType<typeof createProjectSchema>>;

interface ProjectFormProps {
  isOpen: boolean;
  onClose: () => void;
  project?: Project; // For editing existing project
}

const ProjectForm: React.FC<ProjectFormProps> = ({ 
  isOpen, 
  onClose, 
  project 
}) => {
  const { t, isRTL } = useLanguage();
  const { primary } = useColors();
  const [tagInput, setTagInput] = useState('');
  const [memberInput, setMemberInput] = useState('');

  const isEditing = !!project;
  const createProjectMutation = useCreateProject();
  const updateProjectMutation = useUpdateProject();

  const projectSchema = React.useMemo(() => createProjectSchema(t), [t]);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      name: project?.name || '',
      description: project?.description || '',
      status: project?.status || 'planning',
      startDate: project?.startDate ? new Date(project.startDate).toISOString().split('T')[0] : '',
      endDate: project?.endDate ? new Date(project.endDate).toISOString().split('T')[0] : '',
      deadline: project?.deadline ? new Date(project.deadline).toISOString().split('T')[0] : '',
      progress: project?.progress || 0,
      color: project?.color || '#3b82f6',
      teamMembers: project?.teamMembers || [],
      budget: project?.budget || undefined,
      tags: project?.tags || [],
      clientName: project?.clientName || '',
      priority: project?.priority || 'medium'
    }
  });

  const watchedTags = watch('tags');
  const watchedMembers = watch('teamMembers');
  const watchedProgress = watch('progress');

  // Initialize form with project data when editing
  useEffect(() => {
    if (project && isOpen) {
      setValue('name', project.name);
      setValue('description', project.description || '');
      setValue('status', project.status);
      setValue('startDate', project.startDate ? new Date(project.startDate).toISOString().split('T')[0] : '');
      setValue('endDate', project.endDate ? new Date(project.endDate).toISOString().split('T')[0] : '');
      setValue('deadline', project.deadline ? new Date(project.deadline).toISOString().split('T')[0] : '');
      setValue('progress', project.progress);
      setValue('color', project.color);
      setValue('teamMembers', project.teamMembers);
      setValue('budget', project.budget);
      setValue('tags', project.tags);
      setValue('clientName', project.clientName || '');
      setValue('priority', project.priority);
    }
  }, [project, isOpen, setValue]);

  const projectColors = [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', 
    '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16'
  ];

  const statusOptions = [
    { value: 'planning', label: t('projects.status.planning') },
    { value: 'active', label: t('projects.status.active') },
    { value: 'completed', label: t('projects.status.completed') },
    { value: 'cancelled', label: t('projects.status.cancelled') },
    { value: 'onHold', label: t('projects.status.onHold') }
  ];

  const priorityOptions = [
    { value: 'low', label: t('projects.priority.low') },
    { value: 'medium', label: t('projects.priority.medium') },
    { value: 'high', label: t('projects.priority.high') }
  ];

  const handleAddTag = () => {
    if (tagInput.trim() && !watchedTags.includes(tagInput.trim())) {
      setValue('tags', [...watchedTags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setValue('tags', watchedTags.filter(tag => tag !== tagToRemove));
  };

  const handleAddMember = () => {
    if (memberInput.trim() && !watchedMembers.includes(memberInput.trim())) {
      setValue('teamMembers', [...watchedMembers, memberInput.trim()]);
      setMemberInput('');
    }
  };

  const handleRemoveMember = (memberToRemove: string) => {
    setValue('teamMembers', watchedMembers.filter(member => member !== memberToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent, type: 'tag' | 'member') => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (type === 'tag') {
        handleAddTag();
      } else {
        handleAddMember();
      }
    }
  };

  const onSubmit = async (data: ProjectFormData) => {
    try {
      const projectData = {
        ...data,
        startDate: data.startDate ? new Date(data.startDate) : undefined,
        endDate: data.endDate ? new Date(data.endDate) : undefined,
        deadline: data.deadline ? new Date(data.deadline) : undefined,
      };

      if (isEditing && project) {
        await updateProjectMutation.mutateAsync({
          id: project.id,
          updates: projectData
        });
      } else {
        await createProjectMutation.mutateAsync(projectData);
      }
      reset();
      onClose();
    } catch (error) {
      console.error(`Error ${isEditing ? 'updating' : 'creating'} project:`, error);
    }
  };

  const handleClose = () => {
    reset();
    setTagInput('');
    setMemberInput('');
    onClose();
  };

  const modalFooter = (
    <div className={cn(
      'flex items-center gap-3',
      isRTL ? 'flex-row-reverse' : 'flex-row'
    )}>
      <Button
        type="button"
        variant="outline"
        onClick={handleClose}
        disabled={isSubmitting || createProjectMutation.isPending || updateProjectMutation.isPending}
        className="flex-1 sm:flex-none"
      >
        {t('common.cancel')}
      </Button>
      <Button
        type="submit"
        form="project-form"
        disabled={isSubmitting || createProjectMutation.isPending || updateProjectMutation.isPending}
        className="flex-1 sm:flex-none bg-zenith-gradient text-white hover:shadow-zenith-lg"
      >
        <Save className={cn("w-4 h-4", isRTL ? "ml-2" : "mr-2")} />
        {(isSubmitting || createProjectMutation.isPending || updateProjectMutation.isPending)
          ? t('common.saving')
          : isEditing
            ? t('projects.updateProject')
            : t('projects.createProject')
        }
      </Button>
    </div>
  );

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? t('projects.editProject') : t('projects.newProject')}
      description={isEditing ? t('projects.editDescription') : t('projects.newDescription')}
      size="xl"
      footer={modalFooter}
    >

      <form
        id="project-form"
        onSubmit={handleSubmit(onSubmit)}
        className="p-4 sm:p-6 space-y-6"
      >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Project Name */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  {t('projects.name')} *
                </label>
                <input
                  {...register('name')}
                  type="text"
                  placeholder={t('projects.namePlaceholder')}
                  className={cn(
                    "w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20",
                    errors.name && "border-destructive"
                  )}
                />
                {errors.name && (
                  <p className="text-sm text-destructive">{errors.name.message}</p>
                )}
              </div>

              {/* Description */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  {t('projects.form.description')}
                </label>
                <textarea
                  {...register('description')}
                  rows={4}
                  placeholder={t('projects.descriptionPlaceholder')}
                  className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20 resize-none"
                />
              </div>

              {/* Status and Priority */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('projects.status')}
                  </label>
                  <select
                    {...register('status')}
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  >
                    {statusOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('projects.priority')}
                  </label>
                  <select
                    {...register('priority')}
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  >
                    {priorityOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Dates */}
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('projects.startDate')}
                  </label>
                  <input
                    {...register('startDate')}
                    type="date"
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('projects.endDate')}
                  </label>
                  <input
                    {...register('endDate')}
                    type="date"
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('projects.form.deadline')}
                  </label>
                  <input
                    {...register('deadline')}
                    type="date"
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  />
                </div>
              </div>

              {/* Progress */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  {t('projects.form.progress')}: {watchedProgress}%
                </label>
                <input
                  {...register('progress', { valueAsNumber: true })}
                  type="range"
                  min="0"
                  max="100"
                  className="w-full"
                />
              </div>

              {/* Budget and Client */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('projects.budget')}
                  </label>
                  <input
                    {...register('budget', { valueAsNumber: true })}
                    type="number"
                    placeholder="0"
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('projects.client')}
                  </label>
                  <input
                    {...register('clientName')}
                    type="text"
                    placeholder={t('projects.clientPlaceholder')}
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Full Width Sections */}
          <div className="space-y-6">
            {/* Color Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">
                {t('projects.color')}
              </label>
              <div className="flex gap-2 flex-wrap">
                {projectColors.map((color) => (
                  <button
                    key={color}
                    type="button"
                    onClick={() => setValue('color', color)}
                    className={cn(
                      "w-8 h-8 rounded-full border-2 transition-all",
                      watch('color') === color
                        ? "border-foreground scale-110"
                        : "border-border hover:border-foreground/50"
                    )}
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                ))}
              </div>
            </div>

            {/* Team Members */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">
                {t('projects.teamMembers')}
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={memberInput}
                  onChange={(e) => setMemberInput(e.target.value)}
                  onKeyPress={(e) => handleKeyPress(e, 'member')}
                  placeholder={t('projects.addMember')}
                  className="flex-1 px-4 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
                <button
                  type="button"
                  onClick={handleAddMember}
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                >
                  <Users className="w-4 h-4" />
                </button>
              </div>
              {watchedMembers.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {watchedMembers.map((member, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-muted text-muted-foreground rounded-full text-sm"
                    >
                      {member}
                      <button
                        type="button"
                        onClick={() => handleRemoveMember(member)}
                        className="hover:text-destructive"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">
                {t('projects.tags')}
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => handleKeyPress(e, 'tag')}
                  placeholder={t('projects.addTag')}
                  className="flex-1 px-4 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
                <button
                  type="button"
                  onClick={handleAddTag}
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                >
                  <Target className="w-4 h-4" />
                </button>
              </div>
              {watchedTags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {watchedTags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-muted text-muted-foreground rounded-full text-sm"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => handleRemoveTag(tag)}
                        className="hover:text-destructive"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </form>
    </ResponsiveModal>
  );
};

export default ProjectForm;
