import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  duration?: number;
  type?: 'fade' | 'slide' | 'scale' | 'none';
  direction?: 'up' | 'down' | 'left' | 'right';
}

/**
 * PageTransition - Smooth page transition component
 * Provides consistent animations when navigating between pages
 */
const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className,
  duration = 300,
  type = 'fade',
  direction = 'up',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isEntering, setIsEntering] = useState(true);

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => {
      setIsVisible(true);
      setIsEntering(false);
    }, 50);

    return () => clearTimeout(timer);
  }, []);

  const getTransitionClasses = () => {
    const baseClasses = `transition-all duration-${duration} ease-out`;
    
    switch (type) {
      case 'fade':
        return cn(
          baseClasses,
          isVisible ? 'opacity-100' : 'opacity-0'
        );
      
      case 'slide': {
        const slideClasses = {
          up: isVisible ? 'translate-y-0' : 'translate-y-4',
          down: isVisible ? 'translate-y-0' : '-translate-y-4',
          left: isVisible ? 'translate-x-0' : 'translate-x-4',
          right: isVisible ? 'translate-x-0' : '-translate-x-4',
        };
        return cn(
          baseClasses,
          isVisible ? 'opacity-100' : 'opacity-0',
          slideClasses[direction]
        );
      }
      
      case 'scale':
        return cn(
          baseClasses,
          isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
        );
      
      case 'none':
      default:
        return '';
    }
  };

  return (
    <div className={cn(
      getTransitionClasses(),
      className
    )}>
      {children}
    </div>
  );
};

export default PageTransition;
