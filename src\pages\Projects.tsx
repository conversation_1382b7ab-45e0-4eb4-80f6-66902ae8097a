import React from 'react';
import ProjectsList from '@/components/Projects/ProjectsList';
import { useLanguage } from '@/contexts/LanguageContext';
import { MainLayout, ContentContainer, PageHeader, PageTransition } from '@/components/Layout';

const Projects: React.FC = () => {
  const { t } = useLanguage();

  return (
    <MainLayout>
      <PageTransition type="slide" direction="up">
        <ContentContainer>
          <PageHeader
            title={t('projects.title')}
            description={t('projects.description')}
          />
          <ProjectsList />
        </ContentContainer>
      </PageTransition>
    </MainLayout>
  );
};

export default Projects;
